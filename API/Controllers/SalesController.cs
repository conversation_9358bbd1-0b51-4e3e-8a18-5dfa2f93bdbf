using Application.DTOs.Common;
using Application.DTOs.SaleDTOs;
using Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Tags("Sales")]
[Authorize]
public class SalesController(ISaleService saleService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType<PagedResultDto<ViewSaleDto>>(StatusCodes.Status200OK)]
    [Authorize(Policy = "UserOrAbove")]
    public async Task<IActionResult> GetSales(
        [FromQuery] QueryParametersDto queryParameters,
        [FromQuery] string? status,
        [FromQuery] Guid? productId,
        [FromQuery] DateTime? startDate,
        [FromQuery] DateTime? endDate,
        CancellationToken cancellationToken)
    {
        var sales = await saleService.GetSalesPagedAsync(
            queryParameters, 
            status, 
            productId,
            startDate, 
            endDate, 
            cancellationToken);
            
        return Ok(sales);
    }

    [HttpGet("{id}")]
    [ProducesResponseType<ViewSaleDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [Authorize(Policy = "UserOrAbove")]
    public async Task<IActionResult> GetSaleById(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var sale = await saleService.GetSaleByIdAsync(id, cancellationToken);
            return Ok(sale);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }
    
    [HttpPost("draft")]
    [ProducesResponseType<Guid>(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = "UserOrAbove")]
    public async Task<IActionResult> CreateSale(CreateSaleDto saleDto, CancellationToken cancellationToken)
    {
        try
        {
            var saleId = await saleService.CreateSaleAsync(saleDto, cancellationToken);
            return Created($"/api/sales/{saleId}", saleId);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
    
    [HttpPost]
    [ProducesResponseType<Guid>(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = "UserOrAbove")]
    public async Task<IActionResult> CreateCompleteSale(CreateCompleteSaleDto saleDto, CancellationToken cancellationToken)
    {
        try
        {
            var saleId = await saleService.CreateCompleteSaleAsync(saleDto, cancellationToken);
            return Created($"/api/sales/{saleId}", saleId);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("complete")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = "UserOrAbove")]
    public async Task<IActionResult> CompleteSale(CompleteSaleDto completeDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.CompleteSaleAsync(completeDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("cancel")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = "ManagerOrAdmin")]
    public async Task<IActionResult> CancelSale(CancelSaleDto cancelDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.CancelSaleAsync(cancelDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("refund")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = "ManagerOrAdmin")]
    public async Task<IActionResult> RefundSale(RefundSaleDto refundDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.RefundSaleAsync(refundDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [Authorize(Policy = "ManagerOrAdmin")]
    public async Task<IActionResult> UpdateSale(UpdateSaleDto updateDto, CancellationToken cancellationToken)
    {
        try
        {
            await saleService.UpdateSaleAsync(updateDto, cancellationToken);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("aggregated")]
    [ProducesResponseType<AggregatedSalesResultDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = "ManagerOrAdmin")]
    public async Task<IActionResult> GetAggregatedSales(
        [FromQuery] QueryParametersDto queryParameters,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var aggregatedSales = await saleService.GetAggregatedSalesAsync(
                queryParameters, fromDate, toDate, cancellationToken);
            return Ok(aggregatedSales);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
