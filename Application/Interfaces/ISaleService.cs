using Application.DTOs.Common;
using Application.DTOs.SaleDTOs;
using Domain.Enums;

namespace Application.Interfaces;

public interface ISaleService
{
    public Task<Guid> CreateSaleAsync(CreateSaleDto saleDto, CancellationToken cancellationToken = default);
    public Task<Guid> CreateCompleteSaleAsync(CreateCompleteSaleDto saleDto, CancellationToken cancellationToken = default);
    public Task<ViewSaleDto> GetSaleByIdAsync(Guid id, CancellationToken cancellationToken = default);
    public Task CompleteSaleAsync(CompleteSaleDto completeDto, CancellationToken cancellationToken = default);
    public Task CancelSaleAsync(CancelSaleDto cancelDto, CancellationToken cancellationToken = default);
    public Task RefundSaleAsync(RefundSaleDto refundDto, CancellationToken cancellationToken = default);
    public Task UpdateSaleAsync(UpdateSaleDto updateDto, CancellationToken cancellationToken = default);
    
    public Task<PagedResultDto<ViewSaleDto>> GetSalesPagedAsync(
        QueryParametersDto queryParameters,
        string? status,
        Guid? productId,
        DateTime? startDate,
        DateTime? endDate,
        CancellationToken cancellationToken = default);

    public Task<AggregatedSalesResultDto> GetAggregatedSalesAsync(
        QueryParametersDto queryParameters,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default);
}