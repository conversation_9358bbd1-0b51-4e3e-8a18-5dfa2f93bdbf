namespace Application.DTOs.SaleDTOs;

public record AggregatedSaleItemDto(
    Guid ProductId,
    string ProductName,
    int Quantity,
    decimal ActualPrice,
    string ActualCurrency,
    decimal FinalPrice,
    string FinalCurrency,
    decimal Gross,
    string GrossCurrency,
    decimal Discount,
    string DiscountCurrency,
    decimal Revenue,
    string RevenueCurrency,
    decimal Profit,
    string ProfitCurrency,
    List<AggregatedSaleItemDto> Variants
);

public record AggregatedSalesTotalsDto(
    decimal TotalGross,
    string GrossCurrency,
    decimal TotalDiscount,
    string DiscountCurrency,
    decimal TotalRevenue,
    string RevenueCurrency,
    decimal TotalProfit,
    string ProfitCurrency
);

public record AggregatedSalesAveragesDto(
    decimal AverageGross,
    string GrossCurrency,
    decimal AverageDiscount,
    string DiscountCurrency,
    decimal AverageRevenue,
    string RevenueCurrency,
    decimal AverageProfit,
    string ProfitCurrency,
    decimal AverageQuantity
);

public record AggregatedSalesResultDto(
    List<AggregatedSaleItemDto> Items,
    AggregatedSalesTotalsDto Totals,
    AggregatedSalesAveragesDto Averages,
    int TotalCount,
    int PageNumber,
    int PageSize,
    int TotalPages,
    bool HasNextPage,
    bool HasPreviousPage
);
