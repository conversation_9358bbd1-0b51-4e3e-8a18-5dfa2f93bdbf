using Application.DTOs.Common;
using Application.DTOs.SaleDTOs;
using Application.Interfaces;
using Domain.Entities.SaleAggregate;
using Domain.Enums;
using Domain.Exceptions;
using Domain.Interfaces;
using Domain.Managers;
using Domain.Models;
using Domain.ValueObjects;

namespace Application.Services;

public class SaleService(
    ISaleRepository saleRepository,
    IInventoryRepository inventoryRepository,
    IProductRepository productRepository,
    IUnitOfWork unitOfWork,
    ISaleManager saleManager,
    IAuditService auditService) : ISaleService
{
    public async Task<Guid> CreateSaleAsync(CreateSaleDto saleDto, CancellationToken cancellationToken = default)
    {
        var saleItems = new List<SaleItem>();
        if (saleDto.Items is not null)
        {
            foreach (var item in saleDto.Items)
            {
                var inventoryItem =
                    await inventoryRepository.GetInventoryItemByIdAsync(item.InventoryItemId, cancellationToken)
                        .ThrowIfNull(item.InventoryItemId);

                var product = await productRepository
                    .GetByIdOrDefaultDeletedIncludedAsync(inventoryItem.ProductId, cancellationToken)
                    .ThrowIfNull(inventoryItem.ProductId);

                if (inventoryItem.Price is null)
                {
                    throw new InventoryItemPriceNotFoundDomainException(inventoryItem.Id);
                }

                var finalPrice = item.SellingPrice.HasValue && !string.IsNullOrEmpty(item.SellingPriceCurrency)
                    ? new Money(item.SellingPrice.Value,
                        Enum.Parse<CurrencyCode>(item.SellingPriceCurrency, ignoreCase: true))
                    : inventoryItem.Price.SellingPrice;

                saleItems.Add(new SaleItem(product.Id, inventoryItem.Id, product.Name, inventoryItem.Price.ActualPrice,
                    inventoryItem.Price.SellingPrice,
                    finalPrice, item.Quantity));
            }
        }

        var sale = await saleManager.CreateSaleAsync(saleItems, saleDto.ToMoney(), saleDto.ToDiscountType(),
            saleDto.Notes, cancellationToken);

        // Notes are already set in the constructor

        auditService.SetAuditable(sale);
        auditService.SetDomainEvents(sale);

        await saleRepository.AddAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return sale.Id;
    }

    public async Task<Guid> CreateCompleteSaleAsync(CreateCompleteSaleDto saleDto,
        CancellationToken cancellationToken = default)
    {
        var saleItems = new List<SaleItem>();
        foreach (var item in saleDto.Items)
        {
            var inventoryItem =
                await inventoryRepository.GetInventoryItemByIdAsync(item.InventoryItemId, cancellationToken)
                    .ThrowIfNull(item.InventoryItemId);

            var product = await productRepository
                .GetByIdOrDefaultDeletedIncludedAsync(inventoryItem.ProductId, cancellationToken)
                .ThrowIfNull(inventoryItem.ProductId);

            if (inventoryItem.Price is null)
            {
                throw new InventoryItemPriceNotFoundDomainException(inventoryItem.Id);
            }

            var finalPrice = item.SellingPrice.HasValue && !string.IsNullOrEmpty(item.SellingPriceCurrency)
                ? new Money(item.SellingPrice.Value,
                    Enum.Parse<CurrencyCode>(item.SellingPriceCurrency, ignoreCase: true))
                : inventoryItem.Price.SellingPrice;

            saleItems.Add(new SaleItem(product.Id, inventoryItem.Id, product.Name, inventoryItem.Price.ActualPrice,
                inventoryItem.Price.SellingPrice,
                finalPrice, item.Quantity));
        }

        var sale = await saleManager.CreateCompletedSaleAsync(saleItems, saleDto.ToMoney(), saleDto.ToDiscountType(),
            Enum.Parse<CurrencyCode>(saleDto.PaymentCurrency), saleDto.Notes, cancellationToken);

        auditService.SetAuditable(sale);
        auditService.SetDomainEvents(sale);

        await saleRepository.AddAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        return sale.Id;
    }

    public async Task<ViewSaleDto> GetSaleByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(id, cancellationToken)
            .ThrowIfNull(id);

        return ViewSaleDto.FromDomain(sale);
    }

    public async Task<PagedResultDto<ViewSaleDto>> GetSalesPagedAsync(
        QueryParametersDto queryParametersDto,
        string? status,
        Guid? productId,
        DateTime? startDate,
        DateTime? endDate,
        CancellationToken cancellationToken = default)
    {
        var filters = queryParametersDto.Filters ?? new Dictionary<string, string>();
        if (!string.IsNullOrEmpty(status))
        {
            filters["status"] = status;
        }

        if (productId.HasValue)
        {
            filters["productId"] = productId.Value.ToString();
        }

        if (startDate.HasValue)
        {
            filters["startDate"] = startDate.Value.ToUniversalTime().ToString("o");
        }

        if (endDate.HasValue)
        {
            filters["endDate"] = endDate.Value.ToUniversalTime().ToString("o");
        }

        var queryParameters = QueryParameters.Create(
            pageNumber: queryParametersDto.PageNumber,
            pageSize: queryParametersDto.PageSize,
            searchTerm: queryParametersDto.SearchTerm,
            sortBy: queryParametersDto.SortBy,
            sortDirection: queryParametersDto.SortDirection,
            filters: filters
        );

        var (sales, totalCount) = await saleRepository.GetAllPagedAsync(queryParameters, cancellationToken);
        var saleDtos = sales.Select(ViewSaleDto.FromDomain).ToList();

        return PagedResultDto<ViewSaleDto>.Create(
            saleDtos,
            totalCount,
            queryParameters.PageNumber,
            queryParameters.PageSize
        );
    }

    public async Task<AggregatedSalesResultDto> GetAggregatedSalesAsync(
        QueryParametersDto queryParameters,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        CancellationToken cancellationToken = default)
    {
        var sales = await saleRepository.GetSalesForAggregationAsync(fromDate, toDate, cancellationToken);

        // Group by ProductId only
        var productGroups = new Dictionary<Guid, ProductAggregateData>();

        foreach (var sale in sales)
        {
            var saleGross = sale.Items
                .Where(item => item is { Quantity: > 0, FinalPrice.Amount: > 0 })
                .Sum(item => item.FinalPrice.Amount * item.Quantity);

            if (saleGross == 0) continue;

            foreach (var item in sale.Items)
            {
                var productKey = item.ProductId;
                var itemGross = item.FinalPrice.Amount * item.Quantity;
                var allocatedDiscount = itemGross / saleGross * sale.DiscountAmount.Amount;

                if (productGroups.TryGetValue(productKey, out var productData) == false)
                {
                    // Get product name - we'll need to fetch this from the product repository
                    var product = await productRepository.GetByIdOrDefaultDeletedIncludedAsync(item.ProductId, cancellationToken);
                    var productName = product?.Name ?? "Unknown Product";

                    productData = new ProductAggregateData
                    {
                        ProductId = item.ProductId,
                        ProductName = productName,
                        Variants = new Dictionary<(decimal FinalPrice, decimal ActualPrice), VariantAggregateData>()
                    };
                    productGroups[productKey] = productData;
                }
                // Group by final price and actual price within the product (selling price is not relevant for grouping)
                var variantKey = (item.FinalPrice.Amount, item.ActualPrice.Amount);
                if (productData.Variants.TryGetValue(variantKey, out var variantData) == false)
                {
                    variantData = new VariantAggregateData
                    {
                        FinalPrice = item.FinalPrice,
                        ActualPrice = item.ActualPrice,
                        Quantity = 0,
                        TotalGross = 0,
                        TotalDiscount = 0
                    };
                    productData.Variants[variantKey] = variantData;
                }

                variantData.Quantity += item.Quantity;
                variantData.TotalGross += itemGross;
                variantData.TotalDiscount += allocatedDiscount;
            }
        }

        // Convert to DTOs
        var allItems = productGroups.Values.Select(productData =>
        {
            var variants = productData.Variants.Values.Select(variantData =>
            {
                var revenue = variantData.TotalGross - variantData.TotalDiscount;
                var profit = revenue - (variantData.ActualPrice.Amount * variantData.Quantity);

                return new AggregatedSaleItemDto(
                    ProductId: productData.ProductId,
                    ProductName: productData.ProductName,
                    Quantity: variantData.Quantity,
                    ActualPrice: Math.Round(variantData.ActualPrice.Amount, 2),
                    ActualCurrency: variantData.ActualPrice.CurrencyCode.ToString(),
                    FinalPrice: Math.Round(variantData.FinalPrice.Amount, 2),
                    FinalCurrency: variantData.FinalPrice.CurrencyCode.ToString(),
                    Gross: Math.Round(variantData.TotalGross, 2),
                    GrossCurrency: variantData.FinalPrice.CurrencyCode.ToString(),
                    Discount: Math.Round(variantData.TotalDiscount, 2),
                    DiscountCurrency: variantData.FinalPrice.CurrencyCode.ToString(),
                    Revenue: Math.Round(revenue, 2),
                    RevenueCurrency: variantData.FinalPrice.CurrencyCode.ToString(),
                    Profit: Math.Round(profit, 2),
                    ProfitCurrency: variantData.ActualPrice.CurrencyCode.ToString(),
                    Variants: []
                );
            }).ToList();

            // Calculate totals for the product
            var totalQuantity = variants.Sum(v => v.Quantity);
            var totalGross = variants.Sum(v => v.Gross);
            var totalDiscount = variants.Sum(v => v.Discount);
            var totalRevenue = variants.Sum(v => v.Revenue);
            var totalProfit = variants.Sum(v => v.Profit);

            // Use the first variant's currency for the main item (assuming same currency)
            var mainCurrency = variants.FirstOrDefault()?.GrossCurrency ?? "USD";
            var firstVariant = variants.FirstOrDefault();

            return new AggregatedSaleItemDto(
                ProductId: productData.ProductId,
                ProductName: productData.ProductName,
                Quantity: totalQuantity,
                ActualPrice: variants.Count == 1 ? firstVariant?.ActualPrice ?? 0 : 0, // 0 if multiple actual prices
                ActualCurrency: firstVariant?.ActualCurrency ?? "USD",
                FinalPrice: variants.Count == 1 ? firstVariant?.FinalPrice ?? 0 : 0, // 0 if multiple prices
                FinalCurrency: mainCurrency,
                Gross: totalGross,
                GrossCurrency: mainCurrency,
                Discount: totalDiscount,
                DiscountCurrency: mainCurrency,
                Revenue: totalRevenue,
                RevenueCurrency: mainCurrency,
                Profit: totalProfit,
                ProfitCurrency: firstVariant?.ProfitCurrency ?? "USD",
                Variants: variants
            );
        }).ToList();

        // Calculate totals and averages
        var totalCount = allItems.Count;
        var totalGross = allItems.Sum(i => i.Gross);
        var totalDiscount = allItems.Sum(i => i.Discount);
        var totalRevenue = allItems.Sum(i => i.Revenue);
        var totalProfit = allItems.Sum(i => i.Profit);
        var totalQuantity = allItems.Sum(i => i.Quantity);

        var currency = allItems.FirstOrDefault()?.GrossCurrency ?? "USD";

        var totals = new AggregatedSalesTotalsDto(
            TotalGross: Math.Round(totalGross, 2),
            GrossCurrency: currency,
            TotalDiscount: Math.Round(totalDiscount, 2),
            DiscountCurrency: currency,
            TotalRevenue: Math.Round(totalRevenue, 2),
            RevenueCurrency: currency,
            TotalProfit: Math.Round(totalProfit, 2),
            ProfitCurrency: currency
        );

        var averages = new AggregatedSalesAveragesDto(
            AverageGross: totalCount > 0 ? Math.Round(totalGross / totalCount, 2) : 0,
            GrossCurrency: currency,
            AverageDiscount: totalCount > 0 ? Math.Round(totalDiscount / totalCount, 2) : 0,
            DiscountCurrency: currency,
            AverageRevenue: totalCount > 0 ? Math.Round(totalRevenue / totalCount, 2) : 0,
            RevenueCurrency: currency,
            AverageProfit: totalCount > 0 ? Math.Round(totalProfit / totalCount, 2) : 0,
            ProfitCurrency: currency,
            AverageQuantity: totalCount > 0 ? Math.Round((decimal)totalQuantity / totalCount, 2) : 0
        );

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(queryParameters.SearchTerm))
        {
            allItems = allItems
                .Where(item => item.ProductName.Contains(queryParameters.SearchTerm, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(queryParameters.SortBy))
        {
            var isDescending = string.Equals(queryParameters.SortDirection, "desc", StringComparison.OrdinalIgnoreCase);

            allItems = queryParameters.SortBy.ToLowerInvariant() switch
            {
                "quantity" => isDescending
                    ? allItems.OrderByDescending(x => x.Quantity).ToList()
                    : allItems.OrderBy(x => x.Quantity).ToList(),
                "gross" => isDescending
                    ? allItems.OrderByDescending(x => x.Gross).ToList()
                    : allItems.OrderBy(x => x.Gross).ToList(),
                "discount" => isDescending
                    ? allItems.OrderByDescending(x => x.Discount).ToList()
                    : allItems.OrderBy(x => x.Discount).ToList(),
                "revenue" => isDescending
                    ? allItems.OrderByDescending(x => x.Revenue).ToList()
                    : allItems.OrderBy(x => x.Revenue).ToList(),
                "profit" => isDescending
                    ? allItems.OrderByDescending(x => x.Profit).ToList()
                    : allItems.OrderBy(x => x.Profit).ToList(),
                "productname" => isDescending
                    ? allItems.OrderByDescending(x => x.ProductName).ToList()
                    : allItems.OrderBy(x => x.ProductName).ToList(),
                _ => isDescending
                    ? allItems.OrderByDescending(x => x.Revenue).ToList()
                    : allItems.OrderBy(x => x.Revenue).ToList()
            };
        }

        // Update total count after filtering
        var filteredCount = allItems.Count;

        // Apply pagination
        var pagedItems = allItems
            .Skip((queryParameters.PageNumber - 1) * queryParameters.PageSize)
            .Take(queryParameters.PageSize)
            .ToList();

        var totalPages = (int)Math.Ceiling((double)filteredCount / queryParameters.PageSize);

        return new AggregatedSalesResultDto(
            Items: pagedItems,
            Totals: totals,
            Averages: averages,
            TotalCount: filteredCount,
            PageNumber: queryParameters.PageNumber,
            PageSize: queryParameters.PageSize,
            TotalPages: totalPages,
            HasNextPage: queryParameters.PageNumber < totalPages,
            HasPreviousPage: queryParameters.PageNumber > 1
        );
    }

    public async Task CompleteSaleAsync(CompleteSaleDto completeDto, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(completeDto.SaleId, cancellationToken)
            .ThrowIfNull(completeDto.SaleId);

        await saleManager.CompleteSaleAsync(sale, Enum.Parse<CurrencyCode>(completeDto.Currency), cancellationToken);

        auditService.SetAuditable(sale);
        auditService.SetDomainEvents(sale);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task CancelSaleAsync(CancelSaleDto cancelDto, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(cancelDto.SaleId, cancellationToken)
            .ThrowIfNull(cancelDto.SaleId);

        await saleManager.CancelSaleAsync(sale, cancelDto.Reason, cancellationToken);

        auditService.SetAuditable(sale);
        auditService.SetDomainEvents(sale);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task RefundSaleAsync(RefundSaleDto refundDto, CancellationToken cancellationToken = default)
    {
        var sale = await saleRepository.GetByIdOrDefaultAsync(refundDto.SaleId, cancellationToken)
            .ThrowIfNull(refundDto.SaleId);

        await saleManager.RefundSaleAsync(sale, refundDto.Reason, cancellationToken);

        auditService.SetAuditable(sale);
        auditService.SetDomainEvents(sale);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateSaleAsync(UpdateSaleDto updateDto, CancellationToken cancellationToken = default)
    {
        if (updateDto.Items is null)
        {
            return;
        }

        var sale = await saleRepository.GetByIdOrDefaultAsync(updateDto.SaleId, cancellationToken)
            .ThrowIfNull(updateDto.SaleId);

        List<SaleItemUpdateModel> saleItemUpdateModels = [];
        foreach (var item in updateDto.Items)
        {
            var inventoryItem = await inventoryRepository
                .GetInventoryItemByIdAsync(item.InventoryItemId, cancellationToken)
                .ThrowIfNull(item.InventoryItemId);

            var product = await productRepository
                .GetByIdOrDefaultDeletedIncludedAsync(inventoryItem.ProductId, cancellationToken)
                .ThrowIfNull(inventoryItem.ProductId);

            var finalPrice = item.SellingPrice.HasValue && !string.IsNullOrEmpty(item.SellingPriceCurrency)
                ? new Money(item.SellingPrice.Value,
                    Enum.Parse<CurrencyCode>(item.SellingPriceCurrency, ignoreCase: true))
                : inventoryItem.Price.SellingPrice;

            saleItemUpdateModels.Add(new SaleItemUpdateModel(item.InventoryItemId, product.Id, product.Name,
                item.Quantity, finalPrice, inventoryItem.Price.ActualPrice, inventoryItem.Price.SellingPrice));
        }

        Enum.TryParse(updateDto.Status, ignoreCase: true, out SaleStatus status);
        Enum.TryParse(updateDto.PaymentCurrency, ignoreCase: true, out CurrencyCode currencyCode);

        await saleManager.UpdateSaleAsync(sale, saleItemUpdateModels, updateDto.ToMoney(), updateDto.ToDiscountType(),
            updateDto.Notes, status == SaleStatus.Completed, currencyCode, cancellationToken);

        auditService.SetAuditable(sale);
        auditService.SetDomainEvents(sale);

        await saleRepository.UpdateAsync(sale, cancellationToken);
        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}

internal class ProductAggregateData
{
    public Guid ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public Dictionary<(decimal FinalPrice, decimal ActualPrice), VariantAggregateData> Variants { get; set; } = new();
}

internal class VariantAggregateData
{
    public Money FinalPrice { get; set; } = Money.Zero;
    public Money ActualPrice { get; set; } = Money.Zero;
    public int Quantity { get; set; }
    public decimal TotalGross { get; set; }
    public decimal TotalDiscount { get; set; }
}