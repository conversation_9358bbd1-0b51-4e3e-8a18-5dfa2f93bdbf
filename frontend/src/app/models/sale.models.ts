export interface ViewSaleDto {
  id: string;
  items: ViewSaleItemDto[];
  totalAmount: number;
  totalCurrency: string;
  discountAmount: number;
  discountCurrency?: string;
  discountType: DiscountType;
  finalAmount: number;
  finalCurrency: string;
  createdAt: Date;
  status: SaleStatus;
  salesPerson: string;
  notes?: string;
}

export interface ViewSaleItemDto {
  inventoryItemId: string;
  productId: string;
  productName: string;
  quantity: number;
  actualPrice: number;
  actualCurrency: string;
  sellingPrice: number;
  sellingCurrency: string;
  finalPrice: number;
  finalCurrency: string;
  totalPrice: number;
}

export interface CreateSaleDto {
  discountType: DiscountType;
  discountAmount?: number;
  discountCurrency?: string;
  items?: SaleItemDto[];
  notes?: string;
}

export interface CreateCompleteSaleDto {
  items: SaleItemDto[];
  paymentCurrency: string;
  discountType: DiscountType;
  discountAmount?: number;
  discountCurrency?: string;
  salesPerson?: string;
  notes?: string;
}

export interface SaleItemDto {
  inventoryItemId: string;
  productId: string;
  productName: string;
  quantity: number;
  sellingPrice?: number;
  sellingPriceCurrency?: string;
}

export interface UpdateSaleItemPriceDto {
  saleId: string;
  productId: string;
  sellingPrice: number;
  currency: string;
}

export interface CompleteSaleDto {
  saleId: string;
  currency: string;
}

export interface CancelSaleDto {
  saleId: string;
  reason?: string;
}

export interface RefundSaleDto {
  saleId: string;
  reason?: string;
}

export interface ApplySaleDiscountDto {
  saleId: string;
  amount: number;
  currency: string;
}

export interface UpdateSaleDto {
  saleId: string;
  items?: SaleItemDto[];
  discountType?: DiscountType;
  discountAmount?: number;
  discountCurrency?: string;
  notes?: string;
  status?: string;
  paymentCurrency?: string;
}

export interface DateRangeDto {
  startDate: string;
  endDate: string;
}

export enum SaleStatus {
  Pending = 'Pending',
  Completed = 'Completed',
  Cancelled = 'Cancelled',
  Refunded = 'Refunded',
}

export enum DiscountType {
  Fixed = 'Fixed',
  Percentage = 'Percentage',
}

export interface AggregatedSaleItemDto {
  productId: string;
  productName: string;
  quantity: number;
  actualPrice: number;
  actualCurrency: string;
  finalPrice: number;
  finalCurrency: string;
  gross: number;
  grossCurrency: string;
  discount: number;
  discountCurrency: string;
  revenue: number;
  revenueCurrency: string;
  profit: number;
  profitCurrency: string;
  variants: AggregatedSaleItemDto[];
}

export interface AggregatedSalesTotalsDto {
  totalGross: number;
  grossCurrency: string;
  totalDiscount: number;
  discountCurrency: string;
  totalRevenue: number;
  revenueCurrency: string;
  totalProfit: number;
  profitCurrency: string;
}

export interface AggregatedSalesAveragesDto {
  averageGross: number;
  grossCurrency: string;
  averageDiscount: number;
  discountCurrency: string;
  averageRevenue: number;
  revenueCurrency: string;
  averageProfit: number;
  profitCurrency: string;
  averageQuantity: number;
}

export interface AggregatedSalesResultDto {
  items: AggregatedSaleItemDto[];
  totals: AggregatedSalesTotalsDto;
  averages: AggregatedSalesAveragesDto;
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
