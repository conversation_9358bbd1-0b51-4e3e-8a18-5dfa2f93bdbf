import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {environment} from '../../environments/environment';
import {
  ViewSaleDto,
  CreateSaleDto,
  SaleItemDto,
  ApplySaleDiscountDto,
  CompleteSaleDto,
  CancelSaleDto,
  DateRangeDto,
  CreateCompleteSaleDto,
  RefundSaleDto,
  UpdateSaleDto,
  PagedResult,
  QueryParams,
  UpdateSaleItemPriceDto,
  AggregatedSaleItemDto,
  AggregatedSalesResultDto
} from '../models';
import {Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SaleService {
  private readonly baseUrl = `${environment.apiUrl}/sales`;

  constructor(private http: HttpClient) {
  }

  getSalesPaged(queryParams: QueryParams, status?: string, productId?: string, startDate?: Date, endDate?: Date, minAmount?: number, maxAmount?: number, currency?: string): Observable<PagedResult<ViewSaleDto>> {
    let params = new HttpParams()
      .set('pageNumber', queryParams.pageNumber?.toString() || '1')
      .set('pageSize', queryParams.pageSize?.toString() || '10');

    if (queryParams.sortBy) {
      params = params.set('sortBy', queryParams.sortBy);
    }
    if (queryParams.sortDirection) {
      params = params.set('sortDirection', queryParams.sortDirection);
    }
    if (queryParams.searchTerm) {
      params = params.set('searchTerm', queryParams.searchTerm);
    }
    if (queryParams.filters) {
      Object.keys(queryParams.filters).forEach(key => {
        params = params.set(`filters[${key}]`, queryParams.filters![key].toString());
      });
    }
    if (status) {
      params = params.set('filters[status]', status);
    }
    if (productId) {
      params = params.set('filters[productId]', productId);
    }
    if (startDate) {
      params = params.set('filters[startDate]', startDate.toISOString());
    }
    if (endDate) {
      params = params.set('filters[endDate]', endDate.toISOString());
    }
    if (minAmount !== undefined) {
      params = params.set('filters[min]', minAmount.toString());
    }
    if (maxAmount !== undefined) {
      params = params.set('filters[max]', maxAmount.toString());
    }
    if (currency) {
      params = params.set('filters[currency]', currency);
    }

    return this.http.get<PagedResult<ViewSaleDto>>(this.baseUrl, {params});
  }

  getSalesWithPriceFilter(queryParams: QueryParams, minAmount?: number, maxAmount?: number, currency?: string): Observable<PagedResult<ViewSaleDto>> {
    return this.getSalesPaged(queryParams, undefined, undefined, undefined, undefined, minAmount, maxAmount, currency);
  }

  getSales(): Observable<ViewSaleDto[]> {
    return this.http.get<ViewSaleDto[]>(this.baseUrl);
  }

  getSale(id: string): Observable<ViewSaleDto> {
    return this.http.get<ViewSaleDto>(`${this.baseUrl}/${id}`);
  }

  getSalesByDateRange(dateRange: DateRangeDto): Observable<ViewSaleDto[]> {
    const params = new HttpParams()
      .set('startDate', dateRange.startDate)
      .set('endDate', dateRange.endDate);
    return this.http.get<ViewSaleDto[]>(`${this.baseUrl}/date-range`, {params});
  }

  createCompleteSale(sale: CreateCompleteSaleDto): Observable<{ id: string }> {
    return this.http.post<{ id: string }>(this.baseUrl, sale);
  }

  createSale(sale: CreateSaleDto): Observable<{ id: string }> {
    return this.http.post<{ id: string }>(this.baseUrl + `/draft`, sale);
  }

  addItemToSale(saleId: string, item: SaleItemDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/${saleId}/items`, item);
  }

  removeItemFromSale(saleId: string, productId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${saleId}/items/${productId}`);
  }

  applySaleDiscount(discount: ApplySaleDiscountDto): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/discount`, discount);
  }

  completeSale(completion: CompleteSaleDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/complete`, completion);
  }

  cancelSale(cancellation: CancelSaleDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/cancel`, cancellation);
  }

  refundSale(refund: RefundSaleDto): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/refund`, refund);
  }

  updateSale(updateDto: UpdateSaleDto): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}`, updateDto);
  }

  updateSaleItemPrice(updatePriceDto: UpdateSaleItemPriceDto): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/items/price`, updatePriceDto);
  }

  getSalesByProduct(productId: string): Observable<ViewSaleDto[]> {
    return this.http.get<ViewSaleDto[]>(`${this.baseUrl}/product/${productId}`);
  }

  getAggregatedSales(
    pageNumber: number = 1,
    pageSize: number = 10,
    fromDate?: Date,
    toDate?: Date,
    searchTerm?: string,
    sortBy?: string,
    sortDirection?: string
  ): Observable<AggregatedSalesResultDto> {
    let params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString());

    if (fromDate) {
      params = params.set('fromDate', fromDate.toISOString());
    }
    if (toDate) {
      params = params.set('toDate', toDate.toISOString());
    }
    if (searchTerm) {
      params = params.set('searchTerm', searchTerm);
    }
    if (sortBy) {
      params = params.set('sortBy', sortBy);
    }
    if (sortDirection) {
      params = params.set('sortDirection', sortDirection);
    }

    return this.http.get<AggregatedSalesResultDto>(`${this.baseUrl}/aggregated`, { params });
  }
}
