import { Component, computed, inject, OnInit, OnDestroy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { PageHeaderComponent } from '../../../components/page-header/page-header.component';
import { ButtonComponent } from '../../../components/ui/button/button.component';
import { SelectComponent } from '../../../components/ui/select/select.component';
import { SelectItemComponent } from '../../../components/ui/select-item/select-item.component';
import { DatePickerComponent } from '../../../components/ui/date-picker/date-picker.component';
import { SearchBarComponent } from '../../../components/ui/search-bar/search-bar.component';
import { ErrorMessageComponent } from '../../../components/ui/error-message/error-message.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SaleService } from '../../../services';
import { AggregatedSaleItemDto, AggregatedSalesResultDto, AggregatedSalesTotalsDto, AggregatedSalesAveragesDto } from '../../../models';
import { ToastService } from '../../../services';
import {PaginationComponent} from '../../../components/ui/pagination/pagination.component';



@Component({
  selector: 'app-sales-aggregated',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    PageHeaderComponent,
    ButtonComponent,
    SelectComponent,
    SelectItemComponent,
    DatePickerComponent,
    SearchBarComponent,
    ErrorMessageComponent,
    TranslateModule,
    PaginationComponent
  ],
  templateUrl: './sales-aggregated.component.html',
  styleUrls: ['./sales-aggregated.component.css', '../../../../styles/pages.list.styles.css']
})
export class SalesAggregatedComponent implements OnInit, OnDestroy {
  private saleService = inject(SaleService);
  private toastService = inject(ToastService);
  private translate = inject(TranslateService);
  private destroy$ = new Subject<void>();

  loading = signal(true);
  errorMessage = signal('');
  aggregatedItems = signal<AggregatedSaleItemDto[]>([]);
  totals = signal<AggregatedSalesTotalsDto | null>(null);
  averages = signal<AggregatedSalesAveragesDto | null>(null);
  selectedDateRange = signal('');
  fromDate = signal<Date | undefined>(undefined);
  toDate = signal<Date | undefined>(undefined);
  searchTerm = signal('');
  currentPage = signal(1);
  pageSize = signal(10);
  totalPages = signal(0);
  totalCount = signal(0);
  hasNextPage = signal(false);
  hasPreviousPage = signal(false);

  error = computed(() => this.errorMessage() !== '');

  dateRanges = [
    { value: 'today', label: 'COMMON.TODAY' },
    { value: 'week', label: 'COMMON.THIS_WEEK' },
    { value: 'month', label: 'COMMON.THIS_MONTH' },
    { value: 'quarter', label: 'COMMON.THIS_QUARTER' },
    { value: 'year', label: 'COMMON.THIS_YEAR' },
    { value: 'all', label: 'COMMON.ALL_TIME' }
  ];

  sortBy = signal('revenue');
  sortDirection = signal<'asc' | 'desc'>('desc');
  expandedRows = signal<Set<string>>(new Set());

  // Make Math available in template
  Math = Math;

  ngOnInit() {
    this.loadAggregatedData();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onDateRangeChange(range: string) {
    this.selectedDateRange.set(range);
    if (range !== 'custom') {
      // Clear custom dates when switching to predefined ranges
      this.fromDate.set(undefined);
      this.toDate.set(undefined);
    }
    this.loadAggregatedData();
  }

  onFromDateSelect(date: Date) {
    this.fromDate.set(date);
    // Auto-select custom range when date is picked
    if (this.selectedDateRange() !== 'custom') {
      this.selectedDateRange.set('custom');
    }
    this.loadAggregatedData();
  }

  onToDateSelect(date: Date) {
    this.toDate.set(date);
    // Auto-select custom range when date is picked
    if (this.selectedDateRange() !== 'custom') {
      this.selectedDateRange.set('custom');
    }
    this.loadAggregatedData();
  }

  onSearchTerm(term: string) {
    this.searchTerm.set(term);
    this.filterData();
  }

  onClearSearch() {
    this.searchTerm.set('');
    this.filterData();
  }

  onClearFilters() {
    this.searchTerm.set('');
    this.fromDate.set(undefined);
    this.toDate.set(undefined);
    if (this.selectedDateRange() === 'custom') {
      this.selectedDateRange.set('month');
    }
    this.loadAggregatedData();
  }

  onSort(field: string) {
    if (this.sortBy() === field) {
      this.sortDirection.set(this.sortDirection() === 'asc' ? 'desc' : 'asc');
    } else {
      this.sortBy.set(field);
      this.sortDirection.set('desc');
    }
    this.currentPage.set(1); // Reset to first page when sorting
    this.loadAggregatedData();
  }

  refreshData() {
    this.loadAggregatedData();
  }

  private loadAggregatedData() {
    this.loading.set(true);
    this.errorMessage.set('');

    const { fromDate, toDate } = this.getDateRange();

    this.saleService.getAggregatedSales(
      this.currentPage(),
      this.pageSize(),
      fromDate,
      toDate,
      this.searchTerm() || undefined,
      this.sortBy() || undefined,
      this.sortDirection() || undefined
    )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          this.aggregatedItems.set(result.items);
          this.totals.set(result.totals);
          this.averages.set(result.averages);
          this.totalCount.set(result.totalCount);
          this.totalPages.set(result.totalPages);
          this.hasNextPage.set(result.hasNextPage);
          this.hasPreviousPage.set(result.hasPreviousPage);
          this.loading.set(false);
        },
        error: (error) => {
          console.error('Error loading aggregated sales:', error);
          this.errorMessage.set('Failed to load aggregated sales data');
          this.loading.set(false);
          this.toastService.showToast({message: 'Failed to load aggregated sales data', type: "error"});
        }
      });
  }

  private getDateRange(): { fromDate?: Date; toDate?: Date } {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (this.selectedDateRange()) {
      case 'today':
        return { fromDate: today, toDate: new Date(today.getTime() + 24 * 60 * 60 * 1000) };
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        return { fromDate: weekStart, toDate: now };
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        return { fromDate: monthStart, toDate: now };
      case 'quarter':
        const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
        return { fromDate: quarterStart, toDate: now };
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        return { fromDate: yearStart, toDate: now };
      case 'custom':
        return { fromDate: this.fromDate(), toDate: this.toDate() };
      default:
        return {};
    }
  }

  private filterData() {
    this.currentPage.set(1); // Reset to first page when filtering
    this.loadAggregatedData();
  }

  onPageChange(page: number) {
    this.currentPage.set(page);
    this.loadAggregatedData();
  }

  formatCurrency(amount: number, currency?: string): string {
    // Check if the amount has decimal places
    const hasDecimals = amount % 1 !== 0;

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      minimumFractionDigits: hasDecimals ? 2 : 0,
      maximumFractionDigits: 2
    }).format(amount);
  }

  formatNumber(value: number): string {
    // Check if the value has decimal places
    const hasDecimals = value % 1 !== 0;

    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: hasDecimals ? 2 : 0,
      maximumFractionDigits: 2
    }).format(value);
  }

  trackByItemId(index: number, item: AggregatedSaleItemDto): string {
    return `${item.productId}-${item.finalPrice}`;
  }

  toggleRowExpansion(item: AggregatedSaleItemDto) {
    const itemId = this.getItemId(item);
    const expanded = this.expandedRows();
    const newExpanded = new Set(expanded);

    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }

    this.expandedRows.set(newExpanded);
  }

  isRowExpanded(item: AggregatedSaleItemDto): boolean {
    const itemId = this.getItemId(item);
    return this.expandedRows().has(itemId);
  }

  hasVariants(item: AggregatedSaleItemDto): boolean {
    return item.variants && item.variants.length > 0;
  }

  private getItemId(item: AggregatedSaleItemDto): string {
    return `${item.productId}`;
  }
}
