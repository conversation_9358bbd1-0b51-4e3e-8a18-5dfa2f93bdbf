<!-- Page Header -->
<app-page-header
  [title]="'SALES_AGGREGATED.TITLE' | translate"
  [subtitle]="'SALES_AGGREGATED.SUBTITLE' | translate">
  <ng-template #actions>
    <app-button
      (onClick)="refreshData()"
      [disabled]="loading()"
      variant="secondary"
      size="md"
      icon="🔄"
      [text]="'COMMON.REFRESH' | translate"
    ></app-button>
  </ng-template>
</app-page-header>

<!-- Error Message -->
<app-error-message
  *ngIf="error()"
  [message]="errorMessage()"
  (retry)="refreshData()">
</app-error-message>

<!-- Loading State -->
<!--<div *ngIf="loading()" class="loading-overlay">-->
<!--  <div class="loading-spinner">-->
<!--    <div class="spinner"></div>-->
<!--    <span class="loading-text">{{ 'SALES_AGGREGATED.LOADING' | translate }}</span>-->
<!--  </div>-->
<!--</div>-->

<!-- Content -->
<div *ngIf="!error()" class="page-content">

  <!-- Summary Stats -->
  <div class="stats-section">
    <h2 class="section-title">{{ 'SALES_AGGREGATED.SUMMARY' | translate }}</h2>
    <div class="stats-grid">
      <!-- Totals -->
      <div class="stats-card">
        <div class="stats-header">
          <div class="stats-icon">💰</div>
          <div class="stats-title">{{ 'SALES_AGGREGATED.TOTALS' | translate }}</div>
        </div>
        <div class="stats-content">
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.TOTAL_GROSS' | translate }}:</span>
            <span class="stat-value success">{{ formatCurrency(totals()?.totalGross || 0, totals()?.grossCurrency || 'USD') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.TOTAL_DISCOUNT' | translate }}:</span>
            <span class="stat-value warning">{{ formatCurrency(totals()?.totalDiscount || 0, totals()?.discountCurrency || 'USD') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.TOTAL_REVENUE' | translate }}:</span>
            <span class="stat-value primary">{{ formatCurrency(totals()?.totalRevenue || 0, totals()?.revenueCurrency || 'USD') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.TOTAL_PROFIT' | translate }}:</span>
            <span class="stat-value" [class]="(totals()?.totalProfit || 0) >= 0 ? 'success' : 'error'">
              {{ formatCurrency(totals()?.totalProfit || 0, totals()?.profitCurrency || 'USD') }}
            </span>
          </div>
        </div>
      </div>

      <!-- Averages -->
      <div class="stats-card">
        <div class="stats-header">
          <div class="stats-icon">📊</div>
          <div class="stats-title">{{ 'SALES_AGGREGATED.AVERAGES' | translate }}</div>
        </div>
        <div class="stats-content">
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.AVG_GROSS' | translate }}:</span>
            <span class="stat-value">{{ formatCurrency(averages()?.averageGross || 0, averages()?.grossCurrency || 'USD') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.AVG_DISCOUNT' | translate }}:</span>
            <span class="stat-value">{{ formatCurrency(averages()?.averageDiscount || 0, averages()?.discountCurrency || 'USD') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.AVG_REVENUE' | translate }}:</span>
            <span class="stat-value">{{ formatCurrency(averages()?.averageRevenue || 0, averages()?.revenueCurrency || 'USD') }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.AVG_PROFIT' | translate }}:</span>
            <span class="stat-value" [class]="(averages()?.averageProfit || 0) >= 0 ? 'success' : 'error'">
              {{ formatCurrency(averages()?.averageProfit || 0, averages()?.profitCurrency || 'USD') }}
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ 'SALES_AGGREGATED.AVG_QUANTITY' | translate }}:</span>
            <span class="stat-value">{{ formatNumber(averages()?.averageQuantity || 0) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="filters-section">
    <div class="filters-content">
      <div class="search-group">
        <app-search-bar
          [value]="searchTerm()"
          [placeholder]="'SALES_AGGREGATED.SEARCH_PRODUCTS' | translate"
          [showClearButton]="true"
          [loading]="loading()"
          (search)="onSearchTerm($event)"
          (clear)="onClearSearch()"
        ></app-search-bar>
      </div>
      <div class="filter-group">
        <app-select
          [defaultItem]="{text: ('COMMON.CUSTOM' | translate)}"
          [selectedValue]="selectedDateRange()"
          [(ngModel)]="selectedDateRange"
          (onSelect)="onDateRangeChange($event)">
          <app-select-item *ngFor="let range of dateRanges"
                           [text]="range.label | translate"
                           [value]="range.value">
          </app-select-item>
        </app-select>

        <app-date-picker
          *ngIf="!selectedDateRange()"
          [selectedDate]="fromDate()"
          [placeholder]="'COMMON.DATE_FROM' | translate"
          [maxDate]="toDate()"
          (onDateSelect)="onFromDateSelect($event)">
        </app-date-picker>

        <app-date-picker
          *ngIf="!selectedDateRange()"
          [selectedDate]="toDate()"
          [placeholder]="'COMMON.DATE_TO' | translate"
          [minDate]="fromDate()"
          (onDateSelect)="onToDateSelect($event)">
        </app-date-picker>

        <app-button
          *ngIf="!selectedDateRange() && (fromDate() || toDate()) || searchTerm()"
          (onClick)="onClearFilters()"
          icon="❌"
          variant="ghost"
          hover="none"
          size="xs"
          [title]="'COMMON.CLEAR_ALL_FILTERS' | translate"
        >
        </app-button>
      </div>
    </div>
    <!-- Results Summary -->
    <div class="results-summary">
      <span class="results-count">
        {{ 'SALES_AGGREGATED.SHOWING_ITEMS' | translate: { total: aggregatedItems().length } }}
      </span>
      <div class="sort-controls">
        <label class="sort-label">{{ 'COMMON.SORT_BY' | translate }}</label>
        <app-button
          [text]="'COMMON.QUANTITY' | translate"
          [icon]="sortDirection() === 'asc' ? '↑' : '↓'"
          (onClick)="onSort('quantity')"
          hover="none"
          [variant]="sortBy() === 'quantity' ? 'info' : 'secondary'"
          size="xs"
        ></app-button>
        <app-button
          [text]="'SALES_AGGREGATED.GROSS' | translate"
          [icon]="sortDirection() === 'asc' ? '↑' : '↓'"
          (onClick)="onSort('gross')"
          hover="none"
          [variant]="sortBy() === 'gross' ? 'info' : 'secondary'"
          size="xs"
        ></app-button>
        <app-button
          [text]="'SALES_AGGREGATED.REVENUE' | translate"
          [icon]="sortDirection() === 'asc' ? '↑' : '↓'"
          (onClick)="onSort('revenue')"
          hover="none"
          [variant]="sortBy() === 'revenue' ? 'info' : 'secondary'"
          size="xs"
        ></app-button>
        <app-button
          [text]="'SALES_AGGREGATED.PROFIT' | translate"
          [icon]="sortDirection() === 'asc' ? '↑' : '↓'"
          (onClick)="onSort('profit')"
          hover="none"
          [variant]="sortBy() === 'profit' ? 'info' : 'secondary'"
          size="xs"
        ></app-button>
      </div>
    </div>
  </div>

  <!-- Aggregated Items Cards -->
  <div class="page-section">
    <div *ngIf="aggregatedItems().length > 0" class="cards-container">
      <div *ngFor="let item of aggregatedItems(); trackBy: trackByItemId" class="product-card" [class.expanded]="isRowExpanded(item)">

        <!-- Card Header -->
        <div class="card-header" (click)="toggleRowExpansion(item)">
          <div class="product-info">
            <h3 class="product-name">{{ item.productName }}</h3>
          </div>
          <button class="expand-button" [class.expanded]="isRowExpanded(item)" type="button">
            <span class="expand-icon">{{ isRowExpanded(item) ? '▼' : '▶' }}</span>
          </button>
        </div>

        <!-- Card Summary -->
        <div class="card-summary">
          <div class="summary-grid">
            <div class="summary-item">
              <span class="summary-label">{{ 'COMMON.QUANTITY' | translate }}</span>
              <span class="summary-value quantity">{{ item.quantity }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">{{ 'SALES_AGGREGATED.GROSS' | translate }}</span>
              <span class="summary-value success">{{ formatCurrency(item.gross, item.grossCurrency) }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">{{ 'SALES_AGGREGATED.DISCOUNT' | translate }}</span>
              <span class="summary-value warning">{{ formatCurrency(item.discount, item.discountCurrency) }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">{{ 'SALES_AGGREGATED.REVENUE' | translate }}</span>
              <span class="summary-value primary">{{ formatCurrency(item.revenue, item.revenueCurrency) }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">{{ 'SALES_AGGREGATED.PROFIT' | translate }}</span>
              <span class="summary-value" [class]="item.profit >= 0 ? 'success' : 'error'">
                {{ formatCurrency(item.profit, item.profitCurrency) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Expanded Details -->
        <div *ngIf="isRowExpanded(item)" class="card-details">
          <div class="details-header">
            <h4 class="details-title">
              {{ hasVariants(item) ? ('SALES_AGGREGATED.VARIANTS' | translate) : ('SALES_AGGREGATED.DETAILS' | translate) }}
            </h4>
          </div>

          <!-- Variants Table -->
          <div *ngIf="hasVariants(item)" class="variants-table-container">
            <table class="variants-table">
              <thead>
                <tr>
                  <th>{{ 'SALES_AGGREGATED.VARIANT' | translate }}</th>
                  <th>{{ 'SALES_AGGREGATED.ACTUAL_PRICE' | translate }}</th>
                  <th>{{ 'SALES_AGGREGATED.FINAL_PRICE' | translate }}</th>
                  <th>{{ 'COMMON.QUANTITY' | translate }}</th>
                  <th>{{ 'SALES_AGGREGATED.GROSS' | translate }}</th>
                  <th>{{ 'SALES_AGGREGATED.DISCOUNT' | translate }}</th>
                  <th>{{ 'SALES_AGGREGATED.PROFIT' | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let variant of item.variants; let i = index" class="variant-row">
                  <td class="variant-number">{{ i + 1 }}</td>
                  <td class="price-cell">{{ formatCurrency(variant.actualPrice, variant.actualCurrency) }}</td>
                  <td class="price-cell">{{ formatCurrency(variant.finalPrice, variant.finalCurrency) }}</td>
                  <td class="quantity-cell">{{ variant.quantity }}</td>
                  <td class="amount-cell success">{{ formatCurrency(variant.gross, variant.grossCurrency) }}</td>
                  <td class="amount-cell warning">{{ formatCurrency(variant.discount, variant.discountCurrency) }}</td>
                  <td class="amount-cell" [class]="variant.profit >= 0 ? 'success' : 'error'">
                    {{ formatCurrency(variant.profit, variant.profitCurrency) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Main Item Details (when no variants) -->
          <div *ngIf="!hasVariants(item)" class="main-item-details">
            <div class="detail-row">
              <span class="detail-label">{{ 'SALES_AGGREGATED.ACTUAL_PRICE' | translate }}</span>
              <span class="detail-value">{{ formatCurrency(item.actualPrice, item.actualCurrency) }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">{{ 'SALES_AGGREGATED.FINAL_PRICE' | translate }}</span>
              <span class="detail-value">{{ formatCurrency(item.finalPrice, item.finalCurrency) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="aggregatedItems().length === 0" class="empty-state">
      <div class="empty-icon">📊</div>
      <h3 class="empty-title">{{ 'SALES_AGGREGATED.NO_DATA_FOUND' | translate }}</h3>
      <p class="empty-message">{{ 'SALES_AGGREGATED.NO_DATA_MESSAGE' | translate }}</p>
    </div>

    <!-- Pagination -->
    <app-pagination
      [currentPage]="currentPage()"
      [totalPages]="totalPages()"
      (pageChange)="onPageChange($event)">
    </app-pagination>

  </div>
</div>
