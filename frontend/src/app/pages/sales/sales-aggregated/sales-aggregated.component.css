/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stats-card {
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.stats-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.stats-icon {
  font-size: 1.5rem;
}

.stats-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-secondary);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-value.success {
  color: var(--color-success-600);
}

.stat-value.warning {
  color: var(--color-warning-600);
}

.stat-value.error {
  color: var(--color-error-600);
}

.stat-value.primary {
  color: var(--color-primary-600);
}

/* Table Specific Styles */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.product-id {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.inventory-id {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.quantity-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  min-width: 2rem;
}

.price-value {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.amount-value {
  font-weight: 600;
  font-size: 0.875rem;
}

.amount-value.success {
  color: var(--color-success-600);
}

.amount-value.warning {
  color: var(--color-warning-600);
}

.amount-value.error {
  color: var(--color-error-600);
}

.amount-value.primary {
  color: var(--color-primary-600);
}

/* Loading Overlay */
.loading-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: var(--bg-surface);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-secondary);
  border-top: 3px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Cards Layout */
.cards-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.product-card {
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.product-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-secondary);
}

.product-card.expanded {
  border-color: var(--color-primary-600);
  box-shadow: var(--shadow-lg);
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-primary);
}

.card-header:hover {
  background-color: var(--bg-secondary);
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 50%;
  background-color: var(--bg-secondary);
  transition: all 0.2s ease;
  color: var(--text-secondary);
  cursor: pointer;
}

.expand-button:hover {
  background-color: var(--color-primary-600);
  color: white;
}

.expand-button.expanded {
  background-color: var(--color-primary-600);
  color: white;
}

.expand-icon {
  font-size: 0.875rem;
  transition: transform 0.2s ease;
}

/* Card Summary */
.card-summary {
  padding: 1rem 1.5rem;
  background-color: var(--bg-surface);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: center;
  padding: 0.75rem;
  background-color: var(--bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-primary);
}

.summary-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summary-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.summary-value.quantity {
  color: var(--color-info-500);
}

.summary-value.success {
  color: var(--color-success-600);
}

.summary-value.warning {
  color: var(--color-warning-600);
}

.summary-value.primary {
  color: var(--color-primary-600);
}

.summary-value.error {
  color: var(--color-error-600);
}

/* Card Details (Expanded Section) */
.card-details {
  padding: 1rem 1.5rem;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

.details-header {
  margin-bottom: 1rem;
}

.details-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Variants Table */
.variants-table-container {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  overflow: hidden;
}

.variants-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.variants-table th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid var(--border-primary);
  text-align: center;
}

.variants-table td {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid var(--color-neutral-200);
  color: var(--text-primary);
  text-align: center;
}

.variants-table tbody tr:last-child td {
  border-bottom: none;
}

.variants-table tbody tr:hover {
  background-color: var(--bg-secondary);
}

.variant-number {
  font-weight: 600;
  color: var(--color-primary-600);
  text-align: center;
  width: 60px;
}

.price-cell,
.amount-cell {
  font-weight: 600;
  text-align: right;
}

.quantity-cell {
  font-weight: 600;
  text-align: center;
  color: var(--color-info-500);
}

.amount-cell.success {
  color: var(--color-success-600);
}

.amount-cell.warning {
  color: var(--color-warning-600);
}

.amount-cell.error {
  color: var(--color-error-600);
}

/* Main Item Details (when no variants) */
.main-item-details {
  padding: 1rem;
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--color-neutral-200);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.detail-value.quantity {
  color: var(--color-info-500);
}

.detail-value.success {
  color: var(--color-success-600);
}

.detail-value.warning {
  color: var(--color-warning-600);
}

.detail-value.primary {
  color: var(--color-primary-600);
}

.detail-value.error {
  color: var(--color-error-600);
}

/* Responsive Design */
@media (max-width: 768px) {
  .cards-container {
    gap: 0.75rem;
  }

  .card-header {
    padding: 0.75rem 1rem;
  }

  .product-name {
    font-size: 1rem;
  }

  .card-summary {
    padding: 0.75rem 1rem;
  }

  .summary-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .summary-item {
    padding: 0.5rem;
  }

  .summary-value {
    font-size: 0.875rem;
  }

  .card-details {
    padding: 0.75rem 1rem;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    padding: 0.5rem 0;
  }

  .detail-label {
    font-size: 0.75rem;
  }

  .detail-value {
    font-size: 0.875rem;
  }

  /* Responsive Variants Table */
  .variants-table {
    font-size: 0.75rem;
  }

  .variants-table th,
  .variants-table td {
    padding: 0.5rem 0.25rem;
  }

  .variants-table th {
    font-size: 0.625rem;
  }

  .variant-number {
    width: 40px;
  }
}
